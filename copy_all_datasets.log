🎯 Début du téléchargement de tous les datasets
📅 Mon Aug  4 16:46:26     2025

=== Traitement de la section: training ===
Datasets trouvés dans training: 113
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_01_g1
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_28_g1
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_10_g1
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_13_g1
📦 Traitement du dataset: TPI_multiclass_prod_DW000335
   Code extrait: DW00033
   BASE_PATH: Data/Prod_data_dataset/DW-00033
🚀 Lancement de copy.sh pour DW-00033...
✅ Succès pour DW-00033
---
📦 Traitement du dataset: TPI_prod_20250120_PS00383_Z_49000_gr1
   Code extrait: PS00383
   BASE_PATH: Data/Prod_data_dataset/PS-00383
🚀 Lancement de copy.sh pour PS-00383...
✅ Succès pour PS-00383
---
📦 Traitement du dataset: TPI_prod_20250120_SS00374_Z_13000_gr1
   Code extrait: SS00374
   BASE_PATH: Data/Prod_data_dataset/SS-00374
🚀 Lancement de copy.sh pour SS-00374...
✅ Succès pour SS-00374
---
⏭️  Code SS00374 déjà traité, passage au suivant
⏭️  Code SS00374 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250120_SS00376_Z_22000_gr1
   Code extrait: SS00376
   BASE_PATH: Data/Prod_data_dataset/SS-00376
🚀 Lancement de copy.sh pour SS-00376...
✅ Succès pour SS-00376
---
⏭️  Code SS00376 déjà traité, passage au suivant
⏭️  Code SS00376 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250120_SS00380_Z_45000_gr1
   Code extrait: SS00380
   BASE_PATH: Data/Prod_data_dataset/SS-00380
🚀 Lancement de copy.sh pour SS-00380...
✅ Succès pour SS-00380
---
📦 Traitement du dataset: TPI_prod_20250128_PS00389_Z_17000_gr1
   Code extrait: PS00389
   BASE_PATH: Data/Prod_data_dataset/PS-00389
🚀 Lancement de copy.sh pour PS-00389...
✅ Succès pour PS-00389
---
⏭️  Code PS00389 déjà traité, passage au suivant
⏭️  Code PS00389 déjà traité, passage au suivant
⏭️  Code PS00389 déjà traité, passage au suivant
⏭️  Code PS00389 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250128_PS00382_Z_16000_gr1
   Code extrait: PS00382
   BASE_PATH: Data/Prod_data_dataset/PS-00382
🚀 Lancement de copy.sh pour PS-00382...
✅ Succès pour PS-00382
---
⏭️  Code PS00382 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250128_PS00375_Z_21000_gr1
   Code extrait: PS00375
   BASE_PATH: Data/Prod_data_dataset/PS-00375
🚀 Lancement de copy.sh pour PS-00375...
✅ Succès pour PS-00375
---
📦 Traitement du dataset: TPI_prod_20250128_PS00395_Z_6000_gr1
   Code extrait: PS00395
   BASE_PATH: Data/Prod_data_dataset/PS-00395
🚀 Lancement de copy.sh pour PS-00395...
✅ Succès pour PS-00395
---
⏭️  Code PS00395 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250128_PS00398_Z_15000_gr1
   Code extrait: PS00398
   BASE_PATH: Data/Prod_data_dataset/PS-00398
🚀 Lancement de copy.sh pour PS-00398...
✅ Succès pour PS-00398
---
⏭️  Code PS00398 déjà traité, passage au suivant
⏭️  Code PS00398 déjà traité, passage au suivant
⏭️  Code PS00398 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250128_PS00391_Z_16000_gr1
   Code extrait: PS00391
   BASE_PATH: Data/Prod_data_dataset/PS-00391
🚀 Lancement de copy.sh pour PS-00391...
✅ Succès pour PS-00391
---
📦 Traitement du dataset: TPI_prod_20250128_PS00400_Z_46000_gr1
   Code extrait: PS00400
   BASE_PATH: Data/Prod_data_dataset/PS-00400
🚀 Lancement de copy.sh pour PS-00400...
✅ Succès pour PS-00400
---
📦 Traitement du dataset: TPI_prod_20250128_PS00401_Z_7000_gr1
   Code extrait: PS00401
   BASE_PATH: Data/Prod_data_dataset/PS-00401
🚀 Lancement de copy.sh pour PS-00401...
✅ Succès pour PS-00401
---
📦 Traitement du dataset: TPI_prod_20250128_SS00390_Z_19000_gr1
   Code extrait: SS00390
   BASE_PATH: Data/Prod_data_dataset/SS-00390
🚀 Lancement de copy.sh pour SS-00390...
✅ Succès pour SS-00390
---
📦 Traitement du dataset: TPI_prod_20250128_SS00392_Z_45000_gr1
   Code extrait: SS00392
   BASE_PATH: Data/Prod_data_dataset/SS-00392
🚀 Lancement de copy.sh pour SS-00392...
✅ Succès pour SS-00392
---
📦 Traitement du dataset: TPI_prod_20241213_SS00337_Z_7000_gr1
   Code extrait: SS00337
   BASE_PATH: Data/Prod_data_dataset/SS-00337
🚀 Lancement de copy.sh pour SS-00337...
✅ Succès pour SS-00337
---
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
⏭️  Code SS00337 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250204_UW00408_Z_4000_to_10000_gr1
   Code extrait: UW00408
   BASE_PATH: Data/Prod_data_dataset/UW-00408
🚀 Lancement de copy.sh pour UW-00408...
✅ Succès pour UW-00408
---
⏭️  Code UW00408 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250204_UW00409_Z_4000_to_10000_gr1
   Code extrait: UW00409
   BASE_PATH: Data/Prod_data_dataset/UW-00409
🚀 Lancement de copy.sh pour UW-00409...
✅ Succès pour UW-00409
---
⏭️  Code UW00409 déjà traité, passage au suivant
⏭️  Code UW00409 déjà traité, passage au suivant
⏭️  Code UW00409 déjà traité, passage au suivant
⏭️  Code UW00409 déjà traité, passage au suivant
⏭️  Code UW00409 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250217_UW00414_Z_4000_to_10000_gr1
   Code extrait: UW00414
   BASE_PATH: Data/Prod_data_dataset/UW-00414
🚀 Lancement de copy.sh pour UW-00414...
✅ Succès pour UW-00414
---
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
⏭️  Code UW00414 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250304_DW00453_Z_4000_to_10000_gr1
   Code extrait: DW00453
   BASE_PATH: Data/Prod_data_dataset/DW-00453
🚀 Lancement de copy.sh pour DW-00453...
✅ Succès pour DW-00453
---
⏭️  Code DW00453 déjà traité, passage au suivant
⏭️  Code DW00453 déjà traité, passage au suivant
⏭️  Code DW00453 déjà traité, passage au suivant
⏭️  Code DW00453 déjà traité, passage au suivant
⏭️  Code DW00453 déjà traité, passage au suivant
⏭️  Code DW00453 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250304_UW00454_Z_4000_to_10000_gr1
   Code extrait: UW00454
   BASE_PATH: Data/Prod_data_dataset/UW-00454
🚀 Lancement de copy.sh pour UW-00454...
✅ Succès pour UW-00454
---
⏭️  Code UW00454 déjà traité, passage au suivant
⏭️  Code UW00454 déjà traité, passage au suivant
⏭️  Code UW00454 déjà traité, passage au suivant
⏭️  Code UW00454 déjà traité, passage au suivant
⏭️  Code UW00454 déjà traité, passage au suivant
⏭️  Code UW00454 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250317_SS00472_Z_4000_to_10000_gr1
   Code extrait: SS00472
   BASE_PATH: Data/Prod_data_dataset/SS-00472
🚀 Lancement de copy.sh pour SS-00472...
✅ Succès pour SS-00472
---
⏭️  Code SS00472 déjà traité, passage au suivant
⏭️  Code SS00472 déjà traité, passage au suivant
⏭️  Code SS00472 déjà traité, passage au suivant
⏭️  Code SS00472 déjà traité, passage au suivant
⏭️  Code SS00472 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250317_PS00472_Z_4000_to_10000_gr1
   Code extrait: PS00472
   BASE_PATH: Data/Prod_data_dataset/PS-00472
🚀 Lancement de copy.sh pour PS-00472...
✅ Succès pour PS-00472
---
⏭️  Code PS00472 déjà traité, passage au suivant
⏭️  Code PS00472 déjà traité, passage au suivant
⏭️  Code PS00472 déjà traité, passage au suivant
⏭️  Code PS00472 déjà traité, passage au suivant
⏭️  Code PS00472 déjà traité, passage au suivant
⏭️  Code PS00472 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250402_PS00502_Z_4000_to_20000_gr1
   Code extrait: PS00502
   BASE_PATH: Data/Prod_data_dataset/PS-00502
🚀 Lancement de copy.sh pour PS-00502...
✅ Succès pour PS-00502
---
⏭️  Code PS00502 déjà traité, passage au suivant
⏭️  Code PS00502 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250403_SS00504_Z_04000_to_20000_gr1
   Code extrait: SS00504
   BASE_PATH: Data/Prod_data_dataset/SS-00504
🚀 Lancement de copy.sh pour SS-00504...
✅ Succès pour SS-00504
---
⏭️  Code SS00504 déjà traité, passage au suivant
⏭️  Code SS00504 déjà traité, passage au suivant
⏭️  Code SS00504 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250413_SS00511_Z_04000_to_20000_gr1
   Code extrait: SS00511
   BASE_PATH: Data/Prod_data_dataset/SS-00511
🚀 Lancement de copy.sh pour SS-00511...
✅ Succès pour SS-00511
---
⏭️  Code SS00511 déjà traité, passage au suivant
⏭️  Code SS00511 déjà traité, passage au suivant
⏭️  Code SS00511 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250403_PS00514_Z_4000_to_20000_gr1
   Code extrait: PS00514
   BASE_PATH: Data/Prod_data_dataset/PS-00514
🚀 Lancement de copy.sh pour PS-00514...
✅ Succès pour PS-00514
---
⏭️  Code PS00514 déjà traité, passage au suivant
⏭️  Code PS00514 déjà traité, passage au suivant
⏭️  Code PS00514 déjà traité, passage au suivant
=== Fin de la section training ===

=== Traitement de la section: validation ===
Datasets trouvés dans validation: 14
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_11_g1
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_20_g1
⚠️  Impossible d'extraire le code du dataset: TPI_MASBlock_c_2024_08_28_30_g1
📦 Traitement du dataset: TPI_prod_20250413_SS00514_Z_04000_to_20000_gr1
   Code extrait: SS00514
   BASE_PATH: Data/Prod_data_dataset/SS-00514
🚀 Lancement de copy.sh pour SS-00514...
✅ Succès pour SS-00514
---
⏭️  Code SS00514 déjà traité, passage au suivant
⏭️  Code SS00514 déjà traité, passage au suivant
⏭️  Code SS00514 déjà traité, passage au suivant
📦 Traitement du dataset: TPI_prod_20250217_UW00416_Z_4000_to_10000_gr1
   Code extrait: UW00416
   BASE_PATH: Data/Prod_data_dataset/UW-00416
🚀 Lancement de copy.sh pour UW-00416...
✅ Succès pour UW-00416
---
⏭️  Code UW00416 déjà traité, passage au suivant
⏭️  Code UW00416 déjà traité, passage au suivant
⏭️  Code UW00416 déjà traité, passage au suivant
⏭️  Code UW00416 déjà traité, passage au suivant
⏭️  Code UW00416 déjà traité, passage au suivant
⏭️  Code UW00416 déjà traité, passage au suivant
=== Fin de la section validation ===

🎉 Téléchargement terminé!
📅 Mon Aug  4 18:09:20     2025
