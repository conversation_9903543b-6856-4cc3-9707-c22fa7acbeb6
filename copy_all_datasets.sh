#!/bin/bash

# Script pour télécharger tous les datasets listés dans TPI_calibprod_binary_v9.yml
# Ce script parse le fichier YAML et lance copy.sh pour chaque dataset

YAML_FILE="TPI_calibprod_binary_v9.yml"
LOG_FILE="copy_all_datasets.log"

# Fonction pour extraire le code dataset (PS00383, SS00374, etc.) d'un nom de dataset
extract_dataset_code() {
    local dataset_name="$1"
    # Cherche un pattern comme PS00383, SS00374, UW00408, DW00453, etc.
    echo "$dataset_name" | grep -oE '[A-Z]{2}[0-9]{5}' | head -1
}

# Fonction pour formater le code dataset pour le path (PS00383 -> PS-00383)
format_dataset_code() {
    local code="$1"
    if [[ $code =~ ^([A-Z]{2})([0-9]{5})$ ]]; then
        echo "${BASH_REMATCH[1]}-${BASH_REMATCH[2]}"
    else
        echo "$code"
    fi
}

# Fonction pour traiter une section (training ou validation)
process_section() {
    local section="$1"
    echo "=== Traitement de la section: $section ===" | tee -a "$LOG_FILE"
    
    # Extraire les datasets de la section
    local in_section=false
    local datasets=()
    
    while IFS= read -r line; do
        # Détecter le début de la section
        if [[ "$line" == "$section:" ]]; then
            in_section=true
            continue
        fi
        
        # Détecter la fin de la section (nouvelle section ou fin de fichier)
        if [[ $in_section == true && "$line" =~ ^[a-zA-Z] && "$line" != *"- "* ]]; then
            break
        fi
        
        # Extraire les noms de datasets
        if [[ $in_section == true && "$line" =~ ^[[:space:]]*-[[:space:]]*(.*):.*$ ]]; then
            local dataset_name="${BASH_REMATCH[1]}"
            datasets+=("$dataset_name")
        fi
    done < "$YAML_FILE"
    
    echo "Datasets trouvés dans $section: ${#datasets[@]}" | tee -a "$LOG_FILE"
    
    # Traiter chaque dataset
    local processed_codes=()
    for dataset in "${datasets[@]}"; do
        local code=$(extract_dataset_code "$dataset")
        if [[ -n "$code" ]]; then
            # Vérifier si on a déjà traité ce code
            if [[ ! " ${processed_codes[@]} " =~ " ${code} " ]]; then
                local formatted_code=$(format_dataset_code "$code")
                local base_path="Data/Prod_data_dataset/$formatted_code"
                
                echo "📦 Traitement du dataset: $dataset" | tee -a "$LOG_FILE"
                echo "   Code extrait: $code" | tee -a "$LOG_FILE"
                echo "   BASE_PATH: $base_path" | tee -a "$LOG_FILE"
                
                # Lancer copy.sh avec le BASE_PATH approprié
                echo "🚀 Lancement de copy.sh pour $formatted_code..." | tee -a "$LOG_FILE"
                if ./copy.sh "$base_path"; then
                    echo "✅ Succès pour $formatted_code" | tee -a "$LOG_FILE"
                else
                    echo "❌ Échec pour $formatted_code" | tee -a "$LOG_FILE"
                fi
                
                processed_codes+=("$code")
                echo "---" | tee -a "$LOG_FILE"
            else
                echo "⏭️  Code $code déjà traité, passage au suivant" | tee -a "$LOG_FILE"
            fi
        else
            echo "⚠️  Impossible d'extraire le code du dataset: $dataset" | tee -a "$LOG_FILE"
        fi
    done
    
    echo "=== Fin de la section $section ===" | tee -a "$LOG_FILE"
    echo "" | tee -a "$LOG_FILE"
}

# === SCRIPT PRINCIPAL ===
echo "🎯 Début du téléchargement de tous les datasets" | tee "$LOG_FILE"
echo "📅 $(date)" | tee -a "$LOG_FILE"
echo "" | tee -a "$LOG_FILE"

# Vérifier que le fichier YAML existe
if [[ ! -f "$YAML_FILE" ]]; then
    echo "❌ Erreur: Le fichier $YAML_FILE n'existe pas" | tee -a "$LOG_FILE"
    exit 1
fi

# Vérifier que copy.sh existe et est exécutable
if [[ ! -f "copy.sh" ]]; then
    echo "❌ Erreur: Le fichier copy.sh n'existe pas" | tee -a "$LOG_FILE"
    exit 1
fi

if [[ ! -x "copy.sh" ]]; then
    echo "🔧 Rendre copy.sh exécutable..." | tee -a "$LOG_FILE"
    chmod +x copy.sh
fi

# Traiter les sections training et validation
process_section "training"
process_section "validation"

echo "🎉 Téléchargement terminé!" | tee -a "$LOG_FILE"
echo "📅 $(date)" | tee -a "$LOG_FILE"
echo "📋 Consultez le fichier $LOG_FILE pour les détails complets"
